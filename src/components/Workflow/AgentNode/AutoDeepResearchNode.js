// DeepResearchNode.js
import React, { memo, useCallback } from 'react';
import { <PERSON>le, useReactFlow, Position } from 'reactflow';
import { Select } from './utils';
import { Select as AntSelect, Switch, InputNumber } from 'antd';
import styles from './AutoDeepResearchNode.module.css';

function AutoDeepResearchNode({
  id,
  data,
  llm_options,
  name,
  database_options = [],
  mcp_server_options = [],
  positive_prompt = []
}) {
  const { setNodes } = useReactFlow();

  const filteredPositiveOptions = positive_prompt;
  const handleChange = useCallback((event, handleId, nodeId) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          const newData = { ...node.data };
          newData.selects = { ...newData.selects, [handleId]: event.target.value };
          return { ...node, data: newData };
        }
        return node;
      })
    );
  }, [setNodes]);

  // 处理开关变化
  const handleSwitchChange = useCallback((checked, handleId) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === id) {
          const newData = { ...node.data };
          newData.selects = { ...newData.selects, [handleId]: checked };
          return { ...node, data: newData };
        }
        return node;
      })
    );
  }, [setNodes, id]);

  // 处理多选变化
  const handleMultiSelectChange = useCallback((values, handleId) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === id) {
          const newData = { ...node.data };
          newData.selects = { ...newData.selects, [handleId]: values };
          return { ...node, data: newData };
        }
        return node;
      })
    );
  }, [setNodes, id]);

  // 处理数字输入变化
  const handleNumberChange = useCallback((value, handleId) => {
    console.log(`handleNumberChange called: ${handleId} = ${value}`);
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === id) {
          const newData = { ...node.data };
          const currentSelects = { ...newData.selects };
          console.log('当前选择值:', currentSelects);

          // 验证规则
          let validatedValue = value;

          // 先处理数值范围验证
          if (handleId === 'max_source_number') {
            // 最大源数量不能大于最大附件数量
            const maxAttachment = currentSelects['max_attachment_number'] || 0;
            if (validatedValue > maxAttachment && maxAttachment > 0) {
              validatedValue = maxAttachment;
            }
          } else if (handleId === 'max_attachment_number') {
            // 如果修改最大附件数量，需要检查最大源数量是否超出
            const maxSource = currentSelects['max_source_number'] || 0;
            if (maxSource > validatedValue && validatedValue > 0) {
              currentSelects['max_source_number'] = validatedValue;
            }
          }

          // 最后处理互斥逻辑：max_iter 与 max_source_number/max_attachment_number 互斥
          if (handleId === 'max_iter' && validatedValue > 0) {
            // 如果设置了max_iter，清零其他两个值
            currentSelects['max_source_number'] = 0;
            currentSelects['max_attachment_number'] = 0;
            console.log('设置max_iter，清零其他值');
          } else if ((handleId === 'max_source_number' || handleId === 'max_attachment_number') && validatedValue > 0) {
            // 如果设置了max_source_number或max_attachment_number，清零max_iter
            currentSelects['max_iter'] = 0;
            console.log('设置源/附件数量，清零max_iter', { handleId, validatedValue, currentMaxIter: currentSelects['max_iter'] });
          }

          console.log('最终选择值:', { ...currentSelects, [handleId]: validatedValue });

          newData.selects = { ...currentSelects, [handleId]: validatedValue };
          return { ...node, data: newData };
        }
        return node;
      })
    );
  }, [setNodes, id]);




  const selects = data && data.selects ? data.selects : {};

  return (
    <div className={styles.custom_node}>
      <div className={styles.custom_node_header}>{name}</div>
      <div className={styles.handle_with_label_input_top_left_1}>
        <Handle type="target" position={Position.Left} id="query_input" />
        <span className={styles.handle_label_input_top_left_1}>用户输入</span>
      </div>
      <div className={styles.handle_with_label_input_top_left_2}>
        <Handle type="target" position={Position.Left} id="llm_input" />
        <span className={styles.handle_label_input_top_left_2}>模型输入</span>
      </div>
      <div className={styles.custom_node_body}>
        <div className={styles.llm_select_container}>
          <span className={styles.llm_label}>大模型</span>
          <Select
            key="llm_name"
            nodeId={id}
            value={selects['llm_name']}
            handleId="llm_name"
            onChange={handleChange}
            options={llm_options}
            className={styles.llm_select}
          />
        </div>
        <div className={styles.database_select_container}>
          <span className={styles.database_label}>知识库</span>
          <AntSelect
            mode="multiple"
            placeholder="选择知识库"
            value={selects['kb_names'] || []}
            onChange={(values) => handleMultiSelectChange(values, 'kb_names')}
            options={database_options}    
            className="nodrag"
            style={{
              width: '100%',
              minHeight: '32px'
            }}
            maxTagCount="responsive"
            maxTagPlaceholder={(omittedValues) => `+${omittedValues.length}...`}
            removeIcon={<span style={{ fontSize: '10px' }}>×</span>}
            suffixIcon={<span style={{ fontSize: '12px' }}>▼</span>}
          />
        </div>

        <div className={styles.mcp_server_select_container}>
          <span className={styles.mcp_server_label}>MCP服务</span>
          <AntSelect
            mode="multiple"
            placeholder="选择MCP服务"
            value={selects['mcp_server_names'] || []}
            onChange={(values) => handleMultiSelectChange(values, 'mcp_server_names')}
            options={mcp_server_options}
            className="nodrag"
            style={{ 
              width: '100%',
              minHeight: '32px'
            }}
            maxTagCount="responsive"
            maxTagPlaceholder={(omittedValues) => `+${omittedValues.length}...`}
            removeIcon={<span style={{ fontSize: '10px' }}>×</span>}
            suffixIcon={<span style={{ fontSize: '12px' }}>▼</span>}
          />
        </div>

        {filteredPositiveOptions && filteredPositiveOptions.length > 0 && (
          <div className={styles.reflection_prompt_select_container}>
            <span className={styles.reflection_prompt_label}>提示词</span>
            <Select
              key="positive_prompt"
              nodeId={id}
              value={selects['positive_prompt']}
              handleId="positive_prompt"
              onChange={handleChange}
              options={filteredPositiveOptions}
              className={styles.reflection_prompt_select}
            />
          </div>
        )}
        <div className={styles.horizontal_controls_container}>
          <div className={styles.max_iter_container}>
            <span className={styles.max_iter_label}>最大循环轮数</span>
            <InputNumber
              min={0}
              max={100}
              value={selects['max_iter'] || 0}
              onChange={(value) => handleNumberChange(value, 'max_iter')}
              className="nodrag"
              style={{
                width: '100%',
                fontSize: '10px'
              }}
              placeholder="请输入最大循环轮数"
            />
          </div>
          <div className={styles.max_iter_container}>
            <span className={styles.max_iter_label}>最大源数量</span>
            <InputNumber
              min={0}
              max={selects['max_attachment_number'] > 0 ? selects['max_attachment_number'] : 100}
              value={selects['max_source_number'] || 0}
              onChange={(value) => handleNumberChange(value, 'max_source_number')}
              className="nodrag"
              style={{
                width: '100%',
                fontSize: '10px'
              }}
              placeholder="请输入最大源数"
            />
          </div>
          <div className={styles.max_iter_container}>
            <span className={styles.max_iter_label}>最大附件数量</span>
            <InputNumber
              min={0}
              max={1000}
              value={selects['max_attachment_number'] || 0}
              onChange={(value) => handleNumberChange(value, 'max_attachment_number')}
              className="nodrag"
              style={{
                width: '100%',
                fontSize: '10px'
              }}
              placeholder="请输入最大附件数"
            />
          </div>
        </div>
      </div>
      
      <div className={styles.handle_with_label_output_top_right_1}>
        <Handle type="source" position={Position.Right} id="attachment_output" style={{ background: '#555' }} />
        <span className={styles.handle_label_output_top_right_1}>附件输出</span>
      </div>
      <div className={styles.outputHandle} style={{ top: '60px' }}>
        <Handle type="source" position={Position.Right} id="llm_output" style={{ background: '#555' }} />
        <span className={styles.outputHandleLabel}>模型输出</span>
      </div>
    </div>
  );
}

export default memo(AutoDeepResearchNode);