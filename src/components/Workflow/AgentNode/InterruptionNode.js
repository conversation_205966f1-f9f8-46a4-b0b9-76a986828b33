// LLMNODE.js
import React, { memo, useCallback, useState, useEffect, useMemo } from 'react';
import { Handle, useReactFlow, Position } from 'reactflow';
import { Switch } from 'antd';
import { Select } from './utils';
import { useNavigate } from 'react-router-dom';
import styles from './InterruptionNode.module.css';
import { PROMPT_ENDPOINT,PROMPT_EDIT_NAME,REFLECTION_NAME,INTERRUPTION_NODE_NAME,EVENT_ENDPOINT } from '../../Configs/Config';
import { fetchData } from '../../Routers/Router';

function InterruptionNode({ id, data, llm_options, name, positive_prompt_options, agent_type, negative_prompt_options = null, isDebugMode = false }) {
  const { setNodes } = useReactFlow();
  const navigate = useNavigate();
  const [promptParameters, setPromptParameters] = useState([]);
  const [promptOutputParameters, setPromptOutputParameters] = useState([]);
  const [actionNameOptions, setActionNameOptions] = useState([]);

  const filteredPositiveOptions = positive_prompt_options.filter(option => option.task_type !== REFLECTION_NAME);
  // const filteredReflectionOptions = positive_prompt_options.filter(option => option.task_type === REFLECTION_NAME);
  // const reflectionOptionsWithEmpty = [{ label: null, value: null }, ...filteredReflectionOptions];
  const filteredNegativeOptions = negative_prompt_options;

  const handleChange = useCallback((event, handleId, nodeId) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          const newData = { ...node.data };
          newData.selects = { ...newData.selects, [handleId]: event.target.value };
          return { ...node, data: newData };
        }
        return node;
      })
    );
  }, [setNodes]);

  // 处理开关变化
  // const handleSwitchChange = useCallback((checked, handleId) => {
  //   setNodes((nds) =>
  //     nds.map((node) => {
  //       if (node.id === id) {
  //         const newData = { ...node.data };
  //         newData.selects = { ...newData.selects, [handleId]: checked };
  //         return { ...node, data: newData };
  //       }
  //       return node;
  //     })
  //   );
  // }, [setNodes, id]);

  const handleLabelClick = async (promptType) => {
    const positivePrompt = selects[promptType];
    if (positivePrompt) {
      try {
        const data = await fetchData(`${PROMPT_ENDPOINT}/query_by_name/${positivePrompt}`);
        const promptId = data?.id;
        if (promptId) {
          const currentPath = window.location.pathname;
          navigate(`${PROMPT_EDIT_NAME}/${promptId}`, { state: { from: `${currentPath}` } });
        } else {
          console.error('No prompt ID found for the selected positive prompt.');
        }
      } catch (error) {
        console.error('Failed to fetch the prompt ID:', error);
      }
    }
  };

  // 获取action_names选项
  useEffect(() => {
    const fetchActionNames = async () => {
      try {
        const response = await fetchData(`${EVENT_ENDPOINT}/action_names`);
        if (response && response.data && response.data.interruption) {
          // 将数组转换为Select组件需要的格式
          const options = response.data.interruption.map(actionName => ({
            label: actionName,
            value: actionName
          }));
          setActionNameOptions(options);
        }
      } catch (error) {
        console.error('Failed to fetch action names:', error);
        setActionNameOptions([]);
      }
    };

    fetchActionNames();
  }, []);

  // 获取选中的正向提示词的参数
  useEffect(() => {
    const fetchPromptParameters = async () => {
      const positivePrompt = selects['positive_prompt'];
      if (positivePrompt) {
        try {
          const data = await fetchData(`${PROMPT_ENDPOINT}/query_by_name/${positivePrompt}`);
          if (data) {
            setPromptParameters(data.parameters || []);
            setPromptOutputParameters(data.output_parameters || []);
          }
        } catch (error) {
          console.error('Failed to fetch prompt parameters:', error);
        }
      } else {
        setPromptParameters([]);
        setPromptOutputParameters([]);
      }
    };

    fetchPromptParameters();
  }, [data?.selects?.positive_prompt]);

  useEffect(() => {
    if (isDebugMode) {
      console.log(`Debug mode enabled for node ${id}`);
    }
  }, [isDebugMode, id]);

  const selects = data && data.selects ? data.selects : {};

  // 判断是否显示默认的模型输入和输出
  const showModelInput = promptParameters.length === 0;
  const showModelOutput = promptOutputParameters.length === 0;
  
  // 计算最后一个输入handle的位置，用于动态调整body的位置
  const lastHandlePosition = useMemo(() => {
    // 基础handle数量（固定的）
    const baseHandleCount = 2; // 用户输入和附件输入
    // 动态参数handle的数量
    const paramHandleCount = promptParameters.length;
    
    const lastStaticHandlePos = 90; // 附件输入位置
    const modelInputPos = 120; // 模型输入位置
    const dynamicStartPos = 120; // 动态参数起始位置
    
    if (paramHandleCount > 0) {
      // 如果有动态参数，最后一个handle是最后一个参数
      return dynamicStartPos + (paramHandleCount - 1) * 30 + 30; // 额外加30px作为间距
    } else if (showModelInput) {
      // 如果没有动态参数但有模型输入
      return modelInputPos + 30;
    } else {
      // 如果只有固定handle
      return lastStaticHandlePos + 30;
    }
  }, [promptParameters.length, showModelInput]);
  
  // 计算节点的总高度
  const nodeHeight = useMemo(() => {
    // 表单区域的基本高度 (包括内边距、选择器等)
    const formBaseHeight = 260; // 增加基本高度以适应新的action_name选择器
    
    // 根据选择器动态调整
    let formHeight = formBaseHeight;
    
    // 计算负向提示词的高度
    if (filteredNegativeOptions && filteredNegativeOptions.length > 0) {
      formHeight += 60; // 负向提示词选择器的高度
    }
    
    // 计算节点的总高度 = 最后一个handle的位置 + 表单高度 + 额外的间距
    return lastHandlePosition + formHeight + 30; // 额外加30px作为底部间距
  }, [lastHandlePosition, filteredNegativeOptions]);

  return (
    <div className={styles.customNode} style={{ minHeight: `${nodeHeight}px` }}>
      <div className={styles.customNodeHeader}>{INTERRUPTION_NODE_NAME}</div>
      
      {/* 输入连接点区域 */}
      <div className={styles.inputHandle} style={{ top: '60px' }}>
        <Handle type="target" position={Position.Left} id="query_input" />
        <span className={styles.handleLabel}>用户输入</span>
      </div>
      
      <div className={styles.inputHandle} style={{ top: '90px' }}>
        <Handle type="target" position={Position.Left} id="attachment_input" />
        <span className={styles.handleLabel}>附件输入</span>
      </div>
      
      {/* 仅当参数为空时显示模型输入 */}
      {/* {showModelInput && ( */}
        <div className={styles.inputHandle} style={{ top: '120px' }}>
          <Handle type="target" position={Position.Left} id="llm_input" />
          <span className={styles.handleLabel}>模型输入</span>
        </div>
      {/* )} */}
      
      {/* 动态显示参数输入点 */}
      {/* {promptParameters.map((param, index) => (
        <div 
          key={`param_${param.name}`} 
          className={styles.parameterHandle}
          style={{ top: `${120 + index * 30}px` }}
        >
          <Handle type="target" position={Position.Left} id={`param_${param.name}`} />
          <span className={styles.handleLabel}>{param.name}</span>
        </div>
      ))} */}
      
      {/* 仅当输出参数为空时显示模型输出 */}
      {showModelOutput && (
        <div className={styles.outputHandle} style={{ top: '60px' }}>
          <Handle type="source" position={Position.Right} id="llm_output" style={{ background: '#555' }} />
          <span className={styles.outputHandleLabel}>模型输出</span>
        </div>
      )}
      
      {/* 动态显示输出参数 */}
      {promptOutputParameters.map((param, index) => (
        <div 
          key={`output_${param.name}`} 
          className={styles.parameterOutputHandle}
          style={{ top: `${60 + index * 30}px` }}
        >
          <Handle type="source" position={Position.Right} id={`output_${param.name}`} style={{ background: '#555' }} />
          <span className={styles.outputHandleLabel}>{param.name}</span>
        </div>
      ))}
      
      {/* 表单内容区域 - 动态设置marginTop */}
      <div className={styles.customNodeBody} style={{ marginTop: `${lastHandlePosition}px` }}>
        <div className={styles.llmSelectContainer}>
          <span className={styles.llmLabel}>大模型</span>
          <Select
            key="llm_name"
            nodeId={id}
            value={selects['llm_name']}
            handleId="llm_name"
            onChange={handleChange}
            options={llm_options}
          />
        </div>
        <div className={styles.positivePromptSelectContainer}>
          <span className={styles.positivePromptLabel} onClick={() => handleLabelClick('positive_prompt')}>
            正向提示词
            <span className={styles.smallRedText}>(点击修改提示词)</span>
          </span>
          <Select
            key="positive_prompt"
            nodeId={id}
            value={selects['positive_prompt']}
            handleId="positive_prompt"
            onChange={handleChange}
            options={filteredPositiveOptions}
          />
        </div>
        <div className={styles.actionNameSelectContainer}>
          <span className={styles.actionNameLabel}>任务类型</span>
          <Select
            key="action_name"
            nodeId={id}
            value={selects['action_name']}
            handleId="action_name"
            onChange={handleChange}
            options={actionNameOptions}
            allowCustomInput={true}
          />
        </div>
        {filteredNegativeOptions && filteredNegativeOptions.length > 0 && (
          <div className={styles.negativePromptSelectContainer}>
            <span className={styles.negativePromptLabel}>负向提示词</span>
            <Select
              key="negative_prompt"
              nodeId={id}
              value={selects['negative_prompt']}
              handleId="negative_prompt"
              onChange={handleChange}
              options={filteredNegativeOptions}
            />
          </div>
        )}
        {/* <div className={styles.interruptSwitchContainer}>
          <span className={styles.interruptSwitchLabel}>用户交互</span>
          <Switch
            checked={selects['interrupt'] || false}
            onChange={(checked) => handleSwitchChange(checked, 'interrupt')}
            className={styles.interruptSwitch}
          />
        </div> */}
      </div>
    </div>
  );
}

export default memo(InterruptionNode);
