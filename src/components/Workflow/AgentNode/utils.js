import React from 'react';
import { useReactFlow, useStoreApi } from 'reactflow';
import { Select as AntSelect } from 'antd';

export function Select({ value, handleId, nodeId, options, allowCustomInput = false}) {
  const { setNodes } = useReactFlow();
  const store_api = useStoreApi();
  console.log(handleId,value,'valuevaluevalue')
  const onChange = (newValue) => {
    const { nodeInternals } = store_api.getState();
    setNodes(
      Array.from(nodeInternals.values()).map((node) => {
        if (node.id === nodeId) {
          node.data = {
            ...node.data,
            selects: {
              ...node.data.selects,
              [handleId]: newValue,
            },
          };
        }
        return node;
      })
    );
  };

  // 如果允许自定义输入，使用支持自定义输入的Select
  if (allowCustomInput) {
    return (
      <div className="custom-node__select">
        <AntSelect
          className="nodrag"
          onChange={(newValue) => {
            // 如果是数组（tags模式），取第一个值
            const finalValue = Array.isArray(newValue) ? newValue[0] : newValue;
            onChange(finalValue);
          }}
          value={value}
          style={{ width: '75%' }}
          options={options}
          showSearch
          allowClear
          placeholder="选择或输入自定义值"
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
          // 使用 notFoundContent 来提示用户可以输入自定义值
          notFoundContent={
            <div style={{ padding: '8px', color: '#666', fontSize: '12px' }}>
              没有找到匹配项，按回车键添加自定义值
            </div>
          }
          onSearch={(searchValue) => {
            // 当搜索时，如果按回车且没有匹配项，允许添加自定义值
            if (searchValue && !options.some(opt =>
              opt.value.toLowerCase().includes(searchValue.toLowerCase())
            )) {
              // 这里可以添加逻辑来处理自定义输入
            }
          }}
          onInputKeyDown={(e) => {
            if (e.key === 'Enter') {
              const inputValue = e.target.value;
              if (inputValue && inputValue.trim()) {
                // 检查是否已存在该选项
                const existingOption = options.find(opt =>
                  opt.value.toLowerCase() === inputValue.trim().toLowerCase()
                );

                if (!existingOption) {
                  // 如果不存在，添加为自定义值
                  onChange(inputValue.trim());
                  e.preventDefault();
                  e.stopPropagation();
                }
              }
            }
          }}
        />
      </div>
    );
  }

  // 默认的标准选择器
  return (
    <div className="custom-node__select">
      <AntSelect
        className="nodrag"
        onChange={onChange}
        value={value}
        style={{ width: '75%' }}
        options={options}
      />
    </div>
  );
}