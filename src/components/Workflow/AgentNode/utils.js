import React from 'react';
import { useReactFlow, useStoreApi } from 'reactflow';
import { Select as AntSelect } from 'antd';

export function Select({ value, handleId, nodeId, options, allowCustomInput = false}) {
  const { setNodes } = useReactFlow();
  const store_api = useStoreApi();
  console.log(handleId,value,'valuevaluevalue')
  const onChange = (newValue) => {
    const { nodeInternals } = store_api.getState();
    setNodes(
      Array.from(nodeInternals.values()).map((node) => {
        if (node.id === nodeId) {
          node.data = {
            ...node.data,
            selects: {
              ...node.data.selects,
              [handleId]: newValue,
            },
          };
        }
        return node;
      })
    );
  };

  // 如果允许自定义输入，使用支持自定义输入的Select
  if (allowCustomInput) {
    // 创建增强的选项列表，包含当前自定义值（如果存在）
    const enhancedOptions = [...options];

    // 如果当前值不在预定义选项中，将其添加到选项列表中
    if (value && !options.some(opt => opt.value === value)) {
      enhancedOptions.push({
        label: `${value} (自定义)`,
        value: value
      });
    }

    return (
      <div className="custom-node__select">
        <AntSelect
          className="nodrag"
          value={value}
          style={{ width: '75%' }}
          options={enhancedOptions}
          showSearch
          allowClear
          placeholder="选择或输入自定义值"
          filterOption={(input, option) => {
            if (!option) return false;
            return (option.label || '').toLowerCase().includes(input.toLowerCase());
          }}
          onSearch={(searchValue) => {
            // 实时更新搜索值，但不立即保存
            // 这样可以避免输入时自动清空的问题
          }}
          onChange={(selectedValue) => {
            // 只有在明确选择或确认时才更新值
            if (selectedValue !== undefined) {
              onChange(selectedValue);
            }
          }}
          onInputKeyDown={(e) => {
            if (e.key === 'Enter') {
              const inputValue = e.target.value;
              if (inputValue && inputValue.trim()) {
                onChange(inputValue.trim());
                // 阻止默认行为
                e.preventDefault();
                e.stopPropagation();
              }
            }
          }}
          dropdownRender={(menu) => (
            <div>
              {menu}
              <div style={{
                padding: '8px',
                borderTop: '1px solid #f0f0f0',
                color: '#666',
                fontSize: '12px'
              }}>
                提示：输入自定义值后按回车键确认
              </div>
            </div>
          )}
        />
      </div>
    );
  }

  // 默认的标准选择器
  return (
    <div className="custom-node__select">
      <AntSelect
        className="nodrag"
        onChange={onChange}
        value={value}
        style={{ width: '75%' }}
        options={options}
      />
    </div>
  );
}