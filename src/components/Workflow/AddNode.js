import React, { useState } from 'react';
import { IoMdAddCircle, IoIos<PERSON>rrowBack, IoIosArrowForward } from "react-icons/io";
import { useReactFlow, useNodes } from 'reactflow';
import { 
  LLM_NAME, KNOWLEDGE_BASE_LLM_NAME,
  AGENT_ROUTER_NAME,TOOL_NODE_NAME,
   MCP_RESOURCE_NAME,INTERRUPTION_NODE_NAME,
  FILE_PARSE_NAME,DEEP_RESEARCH_NAME,AUTO_DEEP_RESEARCH_NAME
 } from '../Configs/Config';
import './AddNode.css';
import { Tooltip } from 'antd';  // 导入 Tooltip

const AddNode = ({ 
  onAdd, kb_name = null, positive_prompt = null, llm_name=null,negative_prompt = null, reflection_prompt_name = null,
  tool_name=null,mcp_server_name=null,mcp_resource_name=null,image_method_name=null,audio_method_name=null,video_method_name=null,
  internet_search_enabled = true }) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const { project, setViewport } = useReactFlow();
  const nodes = useNodes();

  // 为每个项添加一个唯一的 id
  const data = [
    { id: 1, type: LLM_NAME, name: LLM_NAME,description:"大模型节点，基于提示词生成内容" },
    { id: 2, type: KNOWLEDGE_BASE_LLM_NAME, name: KNOWLEDGE_BASE_LLM_NAME,description:"基于用户的问题，从知识库中搜索内容"},
    { id: 3, type: AGENT_ROUTER_NAME, name: AGENT_ROUTER_NAME,description:"路由节点连接多个大模型节点/知识库节点，路由节点会基于用户的问题自主选择合适节点来完成下游任务" },
    { id: 4, type: TOOL_NODE_NAME, name: TOOL_NODE_NAME,description:"工具节点" },
    { id: 5, type: FILE_PARSE_NAME, name: FILE_PARSE_NAME,description:"文件解析节点" },
    { id: 6, type: MCP_RESOURCE_NAME, name: MCP_RESOURCE_NAME,description:"MCP资源和提示词节点" },
    { id: 7, type: DEEP_RESEARCH_NAME, name: DEEP_RESEARCH_NAME,description:"深度研究节点" },
    { id: 8, type: INTERRUPTION_NODE_NAME, name: INTERRUPTION_NODE_NAME,description:"用户交互节点" },
    { id: 9, type: AUTO_DEEP_RESEARCH_NAME, name: AUTO_DEEP_RESEARCH_NAME,description:"基于用户的问题，模型自主搜索，满足用户需求" },
  ];

  const handleAddNode = (e, agent) => {
    e.stopPropagation();
    const newPosition = calculateNewNodePosition(nodes);
    
    let nodeKbName = null;
    let nodePositivePrompt = positive_prompt;
    if (KNOWLEDGE_BASE_LLM_NAME === agent.type) {
      nodeKbName = kb_name;
      nodePositivePrompt='知识库搜索描述';
    }
    if (AGENT_ROUTER_NAME === agent.type) {
      nodePositivePrompt = '路由提示词';
    }
    
    console.log(agent.type,'agent.type');
    const newNode = {
      id: `node-${Date.now()}`,
      type: agent.type,
      position: newPosition,
      data: {
        selects: {
          'handle-0': 'smoothstep',
          'llm_name': llm_name,
          'kb_name': nodeKbName,
          'positive_prompt': agent.type===TOOL_NODE_NAME ?  null : nodePositivePrompt,
          'reflection_prompt_name': reflection_prompt_name,
          'negative_prompt': negative_prompt,
          'tool_name': agent.type===TOOL_NODE_NAME ? tool_name : null,
          'mcp_server_name': agent.type===TOOL_NODE_NAME || agent.type===MCP_RESOURCE_NAME ? mcp_server_name : null,
          'mcp_resource_name': agent.type===MCP_RESOURCE_NAME ? mcp_resource_name : null,
          'image_method_name': agent.type=== FILE_PARSE_NAME ? image_method_name : null,
          'audio_method_name': agent.type=== FILE_PARSE_NAME ? audio_method_name : null,
          'video_method_name': agent.type=== FILE_PARSE_NAME ? video_method_name : null,
          'internet_search_enabled': agent.type=== DEEP_RESEARCH_NAME ? internet_search_enabled : false,
          'max_iter': agent.type === AUTO_DEEP_RESEARCH_NAME ? 3 : 
                    agent.type === DEEP_RESEARCH_NAME ? 2 : 0,
          'action_name':null
        },
      },
    };
    onAdd(newNode);

    setViewport({
      x: -newPosition.x + window.innerWidth / 2 - 100,
      y: -newPosition.y + window.innerHeight / 2 - 50,
      zoom: 1,
    });
  };

  const calculateNewNodePosition = (nodes) => {
    const padding = 100;
    let x = 0;
    let y = 0;

    if (nodes.length > 0) {
      const rightmostNode = nodes.reduce((max, node) => (node.position.x > max.position.x ? node : max), nodes[0]);
      x = rightmostNode.position.x + rightmostNode.width + padding;
      y = rightmostNode.position.y;
    } else {
      x = window.innerWidth / 2 - 50;
      y = window.innerHeight / 2 - 50;
    }

    return { x, y };
  };

  const toggleSidebar = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className={`sidebar ${isExpanded ? 'expanded' : 'collapsed'}`}>
      <div className="sidebar-header">
        <h2>{isExpanded ? '选择节点' : ''}</h2>
        <button onClick={toggleSidebar} className="toggle-btn">
          {isExpanded ? <IoIosArrowBack /> : <IoIosArrowForward />}
        </button>
      </div>
      <div className="menu-items">
        {data.map((item) => (
          <Tooltip title={item.description} key={item.id}>  {/* 使用 Tooltip 包裹 menu-item */}
            <button 
              key={item.id}  // 使用 item.id 作为唯一的 key
              className="menu-item" 
              onClick={(e) => handleAddNode(e, item)}
            >
              <span className="icon">{item.type.charAt(0)}</span>
              {isExpanded && (
                <>
                  <span className="label">{item.name}</span>
                  <IoMdAddCircle className="add-icon" />
                </>
              )}
            </button>
          </Tooltip>
        ))}
      </div>
    </div>
  );
};

export default AddNode;
